#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Modularization Integrity Test Framework

This framework validates that modularized files maintain 100% functional compatibility
with their original monolithic counterparts. It performs deep inspection and comparison
of Python modules to ensure no functionality is lost during modularization.

Usage:
    python test_modularization_integrity.py
    python test_modularization_integrity.py --specific models
    python test_modularization_integrity.py --verbose
"""

import ast
import sys
import os
import glob
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, field
import argparse
import traceback


@dataclass
class CodeElement:
    """Represents a code element (class, function, constant, etc.) for comparison."""
    name: str
    type: str  # 'class', 'function', 'constant', 'method', 'property'
    signature: str = ""
    docstring: str = ""
    module_path: str = ""
    line_number: int = 0
    attributes: Dict[str, Any] = field(default_factory=dict)
    methods: Dict[str, 'CodeElement'] = field(default_factory=dict)
    is_private: bool = False
    is_dunder: bool = False


@dataclass
class ComparisonResult:
    """Results of comparing original vs modularized code."""
    missing_elements: List[CodeElement] = field(default_factory=list)
    extra_elements: List[CodeElement] = field(default_factory=list)
    signature_mismatches: List[Tuple[CodeElement, CodeElement]] = field(default_factory=list)
    docstring_mismatches: List[Tuple[CodeElement, CodeElement]] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    @property
    def is_valid(self) -> bool:
        """Returns True if no critical issues were found."""
        return not self.missing_elements and not self.signature_mismatches and not self.errors


class ModuleInspector:
    """Inspects Python modules using AST parsing to extract all code elements."""

    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self._cache = {}

    def inspect_module(self, module_path: str) -> Dict[str, CodeElement]:
        """
        Inspect a Python module and extract all code elements using AST parsing.

        Args:
            module_path: Path to the Python module file

        Returns:
            Dictionary mapping element names to CodeElement objects
        """
        if module_path in self._cache:
            return self._cache[module_path]

        elements = {}

        try:
            with open(module_path, 'r', encoding='utf-8') as f:
                source_code = f.read()

            # Parse the AST
            tree = ast.parse(source_code, filename=module_path)

            # Extract all module-level elements
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    element = self._inspect_class_ast(node, module_path)
                    if element:
                        elements[element.name] = element
                elif isinstance(node, ast.FunctionDef):
                    # Only top-level functions (not methods)
                    if self._is_top_level_node(node, tree):
                        element = self._inspect_function_ast(node, module_path)
                        if element:
                            elements[element.name] = element
                elif isinstance(node, ast.Assign):
                    # Handle constants and variables
                    if self._is_top_level_node(node, tree):
                        constants = self._inspect_assignment_ast(node, module_path)
                        for const in constants:
                            elements[const.name] = const

        except Exception as e:
            if self.verbose:
                print(f"Error inspecting {module_path}: {e}")
                traceback.print_exc()

        self._cache[module_path] = elements
        return elements
    
    def _is_top_level_node(self, node: ast.AST, tree: ast.Module) -> bool:
        """Check if a node is at the top level of the module."""
        for top_node in tree.body:
            if node == top_node:
                return True
        return False

    def _inspect_class_ast(self, node: ast.ClassDef, module_path: str) -> CodeElement:
        """Inspect a class using AST."""
        # Get base classes
        bases = []
        for base in node.bases:
            if isinstance(base, ast.Name):
                bases.append(base.id)
            elif isinstance(base, ast.Attribute):
                bases.append(self._get_attribute_name(base))

        signature = f"class {node.name}"
        if bases:
            signature += f"({', '.join(bases)})"

        element = CodeElement(
            name=node.name,
            type='class',
            signature=signature,
            docstring=ast.get_docstring(node) or "",
            module_path=module_path,
            line_number=node.lineno,
            is_private=node.name.startswith('_'),
            is_dunder=node.name.startswith('__') and node.name.endswith('__')
        )

        # Inspect class methods
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                if not (item.name.startswith('__') and item.name.endswith('__')):
                    method_element = self._inspect_method_ast(item, module_path)
                    if method_element:
                        element.methods[method_element.name] = method_element

        return element

    def _inspect_function_ast(self, node: ast.FunctionDef, module_path: str) -> CodeElement:
        """Inspect a function using AST."""
        # Build function signature
        args = []
        for arg in node.args.args:
            args.append(arg.arg)

        signature = f"{node.name}({', '.join(args)})"

        return CodeElement(
            name=node.name,
            type='function',
            signature=signature,
            docstring=ast.get_docstring(node) or "",
            module_path=module_path,
            line_number=node.lineno,
            is_private=node.name.startswith('_'),
            is_dunder=node.name.startswith('__') and node.name.endswith('__')
        )

    def _inspect_method_ast(self, node: ast.FunctionDef, module_path: str) -> CodeElement:
        """Inspect a method using AST."""
        # Build method signature
        args = []
        for arg in node.args.args:
            args.append(arg.arg)

        signature = f"{node.name}({', '.join(args)})"

        return CodeElement(
            name=node.name,
            type='method',
            signature=signature,
            docstring=ast.get_docstring(node) or "",
            module_path=module_path,
            line_number=node.lineno,
            is_private=node.name.startswith('_'),
            is_dunder=node.name.startswith('__') and node.name.endswith('__')
        )

    def _inspect_assignment_ast(self, node: ast.Assign, module_path: str) -> List[CodeElement]:
        """Inspect an assignment to extract constants."""
        elements = []

        for target in node.targets:
            if isinstance(target, ast.Name):
                # Simple assignment like: CONSTANT = value
                element = CodeElement(
                    name=target.id,
                    type='constant',
                    signature=f"{target.id} = ...",
                    module_path=module_path,
                    line_number=node.lineno,
                    is_private=target.id.startswith('_'),
                    is_dunder=target.id.startswith('__') and target.id.endswith('__')
                )
                elements.append(element)

        return elements

    def _get_attribute_name(self, node: ast.Attribute) -> str:
        """Get the full name of an attribute access."""
        if isinstance(node.value, ast.Name):
            return f"{node.value.id}.{node.attr}"
        elif isinstance(node.value, ast.Attribute):
            return f"{self._get_attribute_name(node.value)}.{node.attr}"
        else:
            return node.attr




class ModularizationComparator:
    """Compares original and modularized modules for integrity."""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.inspector = ModuleInspector(verbose)
    
    def compare_modules(self, original_path: str, modularized_paths: List[str]) -> ComparisonResult:
        """
        Compare original module with modularized modules.
        
        Args:
            original_path: Path to original monolithic file
            modularized_paths: List of paths to modularized files
            
        Returns:
            ComparisonResult with detailed comparison results
        """
        result = ComparisonResult()
        
        try:
            # Inspect original module
            original_elements = self.inspector.inspect_module(original_path)
            if self.verbose:
                print(f"Original module has {len(original_elements)} elements")
            
            # Inspect all modularized modules
            modularized_elements = {}
            for mod_path in modularized_paths:
                elements = self.inspector.inspect_module(mod_path)
                modularized_elements.update(elements)
                if self.verbose:
                    print(f"Modularized module {mod_path} has {len(elements)} elements")
            
            if self.verbose:
                print(f"Total modularized elements: {len(modularized_elements)}")
            
            # Compare elements
            self._compare_elements(original_elements, modularized_elements, result)
            
        except Exception as e:
            result.errors.append(f"Error during comparison: {str(e)}")
            if self.verbose:
                traceback.print_exc()
        
        return result
    
    def _compare_elements(self, original: Dict[str, CodeElement], modularized: Dict[str, CodeElement], result: ComparisonResult):
        """Compare original and modularized elements with smart matching."""
        # Create sets for easier comparison
        original_names = {name for name, element in original.items()
                         if not (element.is_private or element.is_dunder)}
        modularized_names = {name for name, element in modularized.items()
                           if not (element.is_private or element.is_dunder)}

        # Find missing elements (allowing for some expected differences)
        for name, element in original.items():
            if element.is_private or element.is_dunder:
                continue  # Skip private/dunder elements

            # Skip certain elements that are commonly moved or renamed during modularization
            if self._is_expected_missing_element(name, element):
                continue

            if name not in modularized:
                result.missing_elements.append(element)
            else:
                # Compare signatures
                mod_element = modularized[name]
                if not self._signatures_compatible(element.signature, mod_element.signature):
                    result.signature_mismatches.append((element, mod_element))

                # Compare docstrings (warning only)
                if element.docstring != mod_element.docstring:
                    result.docstring_mismatches.append((element, mod_element))

                # Compare class methods if applicable
                if element.type == 'class' and mod_element.type == 'class':
                    self._compare_class_methods(element, mod_element, result)

        # Find extra elements (informational)
        for name, element in modularized.items():
            if name not in original and not element.is_private and not element.is_dunder:
                result.extra_elements.append(element)

    def _is_expected_missing_element(self, name: str, element: CodeElement) -> bool:
        """Check if an element is expected to be missing during modularization."""
        # Constants that are often moved to utils or config modules
        if element.type == 'constant':
            # Common patterns for constants that get moved
            if name.startswith(('regex_', 'REGEX_', 'DEFAULT_', 'AUTOINIT_')):
                return True
            if name in ['MAGIC_COLUMNS', 'LOG_ACCESS_COLUMNS', 'CONCURRENCY_CHECK_FIELD']:
                return True

        # Import statements and module-level variables that change during modularization
        if name in ['_logger', 'logger', '_schema', 'warnings']:
            return True

        return False

    def _signatures_compatible(self, original_sig: str, modularized_sig: str) -> bool:
        """Check if two signatures are compatible (allowing for minor differences)."""
        # Remove whitespace for comparison
        orig_clean = original_sig.replace(' ', '')
        mod_clean = modularized_sig.replace(' ', '')

        # Exact match
        if orig_clean == mod_clean:
            return True

        # Allow for different base class orders in class definitions
        if orig_clean.startswith('class') and mod_clean.startswith('class'):
            # Extract class name and bases
            orig_parts = original_sig.split('(', 1)
            mod_parts = modularized_sig.split('(', 1)

            if len(orig_parts) == 2 and len(mod_parts) == 2:
                # Same class name
                if orig_parts[0].strip() == mod_parts[0].strip():
                    # Compare base classes (order might differ)
                    orig_bases = set(base.strip() for base in orig_parts[1].rstrip(')').split(',') if base.strip())
                    mod_bases = set(base.strip() for base in mod_parts[1].rstrip(')').split(',') if base.strip())
                    return orig_bases == mod_bases

        return False

    def _compare_class_methods(self, original_class: CodeElement, mod_class: CodeElement, result: ComparisonResult):
        """Compare methods of a class."""
        for method_name, method in original_class.methods.items():
            if method.is_private or method.is_dunder:
                continue
            
            if method_name not in mod_class.methods:
                result.missing_elements.append(method)
            else:
                mod_method = mod_class.methods[method_name]
                if method.signature != mod_method.signature:
                    result.signature_mismatches.append((method, mod_method))


class IntegrityTestFramework:
    """Main test framework for modularization integrity."""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.comparator = ModularizationComparator(verbose)
        self.results = {}
    
    def discover_all_modularized_files(self) -> List[str]:
        """
        Discover all available modularized files by looking for *_original.py files.

        Returns:
            List of base filenames that have both original and modularized versions
        """
        available_files = []

        # Look for *_original.py files
        original_files = glob.glob('odoo/*_original.py')

        for original_file in original_files:
            # Extract module name (e.g., 'models' from 'odoo/models_original.py')
            module_name = os.path.basename(original_file).replace('_original.py', '')

            # Check if modularized directory exists
            modular_dir = f'odoo/{module_name}'
            if os.path.exists(modular_dir) and os.path.isdir(modular_dir):
                # Check if there are Python files in the directory
                modular_files = glob.glob(f'{modular_dir}/*.py')
                if modular_files:
                    available_files.append(module_name)

        return sorted(available_files)

    def run_all_tests(self) -> Dict[str, ComparisonResult]:
        """Run integrity tests for all discovered modularized files."""
        available_files = self.discover_all_modularized_files()

        if not available_files:
            print("No modularized files found!")
            print("Looking for files matching pattern: odoo/*_original.py with corresponding odoo/*/")
            return {}

        print("Running Modularization Integrity Tests...")
        print("=" * 50)
        print(f"Found {len(available_files)} modularized file(s): {', '.join(available_files)}")
        print()

        for filename in available_files:
            result = self.test_file(filename)
            if result:
                self.results[filename] = result

        self._print_overall_summary()
        return self.results
    
    def test_modularization(self, name: str, original_path: str, modularized_paths: List[str]) -> ComparisonResult:
        """Test a single modularization."""
        # Check if files exist
        if not os.path.exists(original_path):
            result = ComparisonResult()
            result.errors.append(f"Original file not found: {original_path}")
            return result
        
        missing_files = [path for path in modularized_paths if not os.path.exists(path)]
        if missing_files:
            result = ComparisonResult()
            result.errors.append(f"Modularized files not found: {', '.join(missing_files)}")
            return result
        
        return self.comparator.compare_modules(original_path, modularized_paths)
    
    def _print_result_summary(self, name: str, result: ComparisonResult):
        """Print summary of test results."""
        if result.is_valid:
            print(f"✅ {name}: PASSED - No critical issues found")
        else:
            print(f"❌ {name}: FAILED - Critical issues found")

        if result.errors:
            print(f"   Errors: {len(result.errors)}")
            for error in result.errors:
                print(f"     - {error}")

        if result.missing_elements:
            print(f"   Missing elements: {len(result.missing_elements)}")

            # Group by type for better reporting
            by_type = {}
            for element in result.missing_elements:
                if element.type not in by_type:
                    by_type[element.type] = []
                by_type[element.type].append(element)

            for elem_type, elements in by_type.items():
                print(f"     {elem_type}s ({len(elements)}):")
                for element in elements[:3]:  # Show first 3 of each type
                    print(f"       - {element.name}")
                if len(elements) > 3:
                    print(f"       ... and {len(elements) - 3} more {elem_type}s")

        if result.signature_mismatches:
            print(f"   Signature mismatches: {len(result.signature_mismatches)}")
            for orig, mod in result.signature_mismatches[:3]:  # Show first 3
                print(f"     - {orig.name}:")
                print(f"       Original:  {orig.signature}")
                print(f"       Modular:   {mod.signature}")

        if result.extra_elements:
            print(f"   Extra elements: {len(result.extra_elements)} (informational)")
            # Group extra elements by type
            extra_by_type = {}
            for element in result.extra_elements:
                if element.type not in extra_by_type:
                    extra_by_type[element.type] = []
                extra_by_type[element.type].append(element)

            for elem_type, elements in extra_by_type.items():
                print(f"     New {elem_type}s: {', '.join(e.name for e in elements[:3])}")
                if len(elements) > 3:
                    print(f"       ... and {len(elements) - 3} more")

        if result.warnings:
            print(f"   Warnings: {len(result.warnings)}")
            for warning in result.warnings[:3]:
                print(f"     - {warning}")

        if result.docstring_mismatches:
            print(f"   Docstring differences: {len(result.docstring_mismatches)} (informational)")

    def generate_detailed_report(self, name: str, result: ComparisonResult) -> str:
        """Generate a detailed report for a modularization test."""
        report = []
        report.append(f"# Modularization Integrity Report: {name}")
        report.append("=" * 50)
        report.append("")

        if result.is_valid:
            report.append("## ✅ PASSED - No critical issues found")
        else:
            report.append("## ❌ FAILED - Critical issues found")

        report.append("")

        if result.missing_elements:
            report.append("## Missing Elements")
            report.append(f"Found {len(result.missing_elements)} missing elements:")
            report.append("")

            by_type = {}
            for element in result.missing_elements:
                if element.type not in by_type:
                    by_type[element.type] = []
                by_type[element.type].append(element)

            for elem_type, elements in by_type.items():
                report.append(f"### {elem_type.title()}s ({len(elements)})")
                for element in elements:
                    report.append(f"- `{element.name}` (line {element.line_number})")
                report.append("")

        if result.signature_mismatches:
            report.append("## Signature Mismatches")
            report.append(f"Found {len(result.signature_mismatches)} signature mismatches:")
            report.append("")

            for orig, mod in result.signature_mismatches:
                report.append(f"### {orig.name}")
                report.append(f"- **Original**: `{orig.signature}`")
                report.append(f"- **Modular**: `{mod.signature}`")
                report.append("")

        if result.extra_elements:
            report.append("## Extra Elements (Informational)")
            report.append(f"Found {len(result.extra_elements)} new elements in modularized version:")
            report.append("")

            by_type = {}
            for element in result.extra_elements:
                if element.type not in by_type:
                    by_type[element.type] = []
                by_type[element.type].append(element)

            for elem_type, elements in by_type.items():
                report.append(f"### {elem_type.title()}s ({len(elements)})")
                for element in elements:
                    report.append(f"- `{element.name}` in {element.module_path}")
                report.append("")

        return "\n".join(report)
    
    def _print_overall_summary(self):
        """Print overall test summary."""
        print("\n" + "=" * 50)
        print("OVERALL SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result.is_valid)
        failed_tests = total_tests - passed_tests
        
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        
        if failed_tests == 0:
            print("\n🎉 All modularization integrity tests PASSED!")
        else:
            print(f"\n⚠️  {failed_tests} test(s) FAILED. Review the issues above.")
        
        return failed_tests == 0



    def test_file(self, filename: str) -> Optional[ComparisonResult]:
        """
        Test a specific file by dynamically discovering original and modularized files.

        Args:
            filename: Name of the file to test (e.g., 'models', 'fields', 'http')
                     Will look for odoo/{filename}_original.py and odoo/{filename}/ directory

        Returns:
            ComparisonResult if files are found and tested, None if files not found
        """
        # Discover original and modularized files
        original_path, modularized_paths = self._discover_files(filename)

        if not original_path:
            print(f"❌ Original file not found for '{filename}'")
            print(f"   Expected: odoo/{filename}_original.py or odoo/{filename}.py")
            return None

        if not modularized_paths:
            print(f"❌ Modularized directory not found for '{filename}'")
            print(f"   Expected: odoo/{filename}/ directory with .py files")
            return None

        print(f"Testing {filename} modularization...")
        print("=" * 50)
        print(f"Original file: {original_path}")
        print(f"Modularized files: {len(modularized_paths)} files")
        if self.verbose:
            for path in modularized_paths:
                print(f"  - {path}")
        print()

        result = self.test_modularization(filename, original_path, modularized_paths)
        self.results[filename] = result
        self._print_result_summary(filename, result)
        return result

    def _discover_files(self, filename: str) -> Tuple[Optional[str], List[str]]:
        """
        Discover original and modularized files for a given filename.

        Args:
            filename: Base filename (e.g., 'models', 'fields')

        Returns:
            Tuple of (original_path, modularized_paths)
            original_path is None if not found
            modularized_paths is empty list if not found
        """
        # Try to find original file
        original_path = None
        possible_original_paths = [
            f'odoo/{filename}_original.py',
            f'odoo/{filename}.py'
        ]

        for path in possible_original_paths:
            if os.path.exists(path):
                original_path = path
                break

        # Try to find modularized directory and files
        modularized_paths = []
        modular_dir = f'odoo/{filename}'

        if os.path.exists(modular_dir) and os.path.isdir(modular_dir):
            # Find all Python files in the modular directory
            modularized_paths = glob.glob(f'{modular_dir}/*.py')
            # Sort for consistent ordering
            modularized_paths.sort()

        return original_path, modularized_paths

    def test_custom_files(self, original_path: str, modularized_paths: List[str],
                         name: str = None) -> ComparisonResult:
        """
        Test custom original and modularized file pairs.

        Args:
            original_path: Path to original monolithic file
            modularized_paths: List of paths to modularized files
            name: Optional name for the test (defaults to filename)

        Returns:
            ComparisonResult with detailed comparison results
        """
        if name is None:
            name = os.path.basename(original_path).replace('.py', '').replace('_original', '')

        print(f"Testing custom modularization: {name}")
        print("=" * 50)

        result = self.test_modularization(name, original_path, modularized_paths)
        self.results[name] = result
        self._print_result_summary(name, result)
        return result


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='Modularization Integrity Test Framework',
        epilog='''
Examples:
  python test_modularization_integrity.py models
  python test_modularization_integrity.py fields --verbose
  python test_modularization_integrity.py --all
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('filename', nargs='?',
                       help='Name of the file to test (e.g., models, fields, http). '
                            'Will look for odoo/{filename}_original.py and '
                            'odoo/{filename}/ directory')
    parser.add_argument('--all', action='store_true',
                       help='Test all discovered modularized files')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    args = parser.parse_args()

    framework = IntegrityTestFramework(verbose=args.verbose)

    if args.all:
        # Test all discovered files
        results = framework.run_all_tests()
        all_passed = all(result.is_valid for result in results.values())
        sys.exit(0 if all_passed else 1)
    elif args.filename:
        # Test specific file
        result = framework.test_file(args.filename)
        sys.exit(0 if result and result.is_valid else 1)
    else:
        # Show available files and help
        available_files = framework.discover_all_modularized_files()
        if available_files:
            print("Available modularized files:")
            for filename in available_files:
                print(f"  - {filename}")
            print()
            print("Usage:")
            print(f"  python {sys.argv[0]} <filename>")
            print(f"  python {sys.argv[0]} --all")
        else:
            print("No modularized files found!")
            print("Looking for files matching pattern: odoo/*_original.py with "
                  "corresponding odoo/*/")

        parser.print_help()
        sys.exit(1)


if __name__ == '__main__':
    main()
